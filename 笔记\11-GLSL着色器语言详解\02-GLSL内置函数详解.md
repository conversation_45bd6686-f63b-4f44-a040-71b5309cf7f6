# GLSL 内置函数详解

## 概述

GLSL (OpenGL Shading Language) 提供了丰富的内置函数库，这些函数经过 GPU 硬件优化，能够高效地执行各种数学运算、纹理采样、几何变换等操作。本文档详细介绍 GLSL 中的各类函数及其使用方法。

## 1. 数学函数 (Mathematical Functions)

### 1.1 基础数学函数

#### abs() - 绝对值

```glsl
float abs(float x)
vec2 abs(vec2 x)
vec3 abs(vec3 x)
vec4 abs(vec4 x)
```

**功能**: 返回参数的绝对值
**示例**:

```glsl
float result = abs(-3.5);                    // 返回 3.5
vec3 result = abs(vec3(-1.0, 2.0, -3.0));   // 返回 vec3(1.0, 2.0, 3.0)
```

#### sign() - 符号函数

```glsl
float sign(float x)
vec2 sign(vec2 x)
vec3 sign(vec3 x)
vec4 sign(vec4 x)
```

**功能**: 返回参数的符号，正数返回 1.0，负数返回-1.0，零返回 0.0
**示例**:

```glsl
float result = sign(-5.0);                   // 返回 -1.0
vec3 result = sign(vec3(-2.0, 0.0, 3.0));   // 返回 vec3(-1.0, 0.0, 1.0)
```

#### floor() - 向下取整

```glsl
float floor(float x)
vec2 floor(vec2 x)
vec3 floor(vec3 x)
vec4 floor(vec4 x)
```

**功能**: 返回小于或等于参数的最大整数值
**示例**:

```glsl
float result = floor(3.7);                  // 返回 3.0
float result2 = floor(-2.3);                // 返回 -3.0
vec2 result = floor(vec2(1.8, -0.5));       // 返回 vec2(1.0, -1.0)
```

#### ceil() - 向上取整

```glsl
float ceil(float x)
vec2 ceil(vec2 x)
vec3 ceil(vec3 x)
vec4 ceil(vec4 x)
```

**功能**: 返回大于或等于参数的最小整数值
**示例**:

```glsl
float result = ceil(3.2);                   // 返回 4.0
float result2 = ceil(-2.7);                 // 返回 -2.0
vec2 result = ceil(vec2(1.1, -0.9));        // 返回 vec2(2.0, 0.0)
```

#### fract() - 小数部分

```glsl
float fract(float x)
vec2 fract(vec2 x)
vec3 fract(vec3 x)
vec4 fract(vec4 x)
```

**功能**: 返回参数的小数部分，等价于 x - floor(x)
**示例**:

```glsl
float result = fract(3.7);                  // 返回 0.7
float result2 = fract(-2.3);                // 返回 0.7 (因为 -2.3 - (-3.0) = 0.7)
vec2 result = fract(vec2(2.5, -1.8));       // 返回 vec2(0.5, 0.2)
```

#### mod() - 取模运算

```glsl
float mod(float x, float y)
vec2 mod(vec2 x, vec2 y)
vec3 mod(vec3 x, vec3 y)
vec4 mod(vec4 x, vec4 y)
```

**功能**: 返回 x - y _ floor(x/y)，即浮点数取模运算
**数学定义**: mod(x, y) = x - y _ floor(x/y)

### mod() 函数的各种运算类型详解

#### 1. 基础取模运算

// 正数情况 - 两者结果相同
// 取模 (mod) - GLSL 中的 mod()函数
// 定义：mod(x, y) = x - y \* floor(x/y)
// 结果的符号总是与除数(y)相同

// 取余 (remainder) - 类似 C 语言的%运算符
// 定义：remainder(x, y) = x - y \* trunc(x/y)  
// 结果的符号总是与被除数(x)相同
mod(7.0, 3.0) // = 1.0//取模
remainder(7.0, 3.0) // = 1.0//取余

mod(5.0, 2.0) // = 1.0  
remainder(5.0, 2.0) // = 1.0

// 负被除数情况 - 结果不同
mod(-7.0, 3.0) // = 2.0 (正数，符号跟除数)
remainder(-7.0, 3.0) // = -1.0 (负数，符号跟被除数)

mod(-5.0, 3.0) // = 1.0 (正数)
remainder(-5.0, 3.0) // = -2.0 (负数)

// 负除数情况 - 结果也不同
mod(7.0, -3.0) // = -2.0 (负数，符号跟除数)
remainder(7.0, -3.0) // = 1.0 (正数，符号跟被除数)

// 双负数情况
mod(-7.0, -3.0) // = -1.0 (负数，符号跟除数)
remainder(-7.0, -3.0)// = -1.0 (负数，符号跟被除数，这种情况下相同)

```glsl
// 正数取模
float result1 = mod(5.0, 3.0);              // 返回 2.0 (5 - 3*1 = 2)
float result2 = mod(7.5, 2.0);              // 返回 1.5 (7.5 - 2*3 = 1.5)
float result3 = mod(10.0, 4.0);             // 返回 2.0 (10 - 4*2 = 2)

// 小数取模
float result4 = mod(3.7, 1.0);              // 返回 0.7 (3.7 - 1*3 = 0.7)
float result5 = mod(2.5, 0.5);              // 返回 0.0 (2.5 - 0.5*5 = 0)
```

#### 2. 负数取模运算

```glsl
// 负被除数
float result1 = mod(-5.0, 3.0);             // 返回 1.0 (-5 - 3*(-2) = 1)
float result2 = mod(-7.5, 2.0);             // 返回 0.5 (-7.5 - 2*(-4) = 0.5)

// 负除数
float result3 = mod(5.0, -3.0);             // 返回 -1.0 (5 - (-3)*(-2) = -1)
float result4 = mod(7.0, -2.0);             // 返回 -1.0 (7 - (-2)*(-4) = -1)

// 双负数
float result5 = mod(-5.0, -3.0);            // 返回 -2.0 (-5 - (-3)*1 = -2)
```

#### 3. 特殊值处理

```glsl
// 0开头的特殊情况
float result1 = mod(0.0, 3.0);              // 返回 0.0
float result2 = mod(0.5, 1.0);              // 返回 0.5
float result3 = mod(0.1, 0.3);              // 返回 0.1

// 被除数小于除数
float result4 = mod(2.0, 5.0);              // 返回 2.0 (2 - 5*0 = 2)
float result5 = mod(0.3, 1.0);              // 返回 0.3 (0.3 - 1*0 = 0.3)

// 相等情况
float result6 = mod(5.0, 5.0);              // 返回 0.0 (5 - 5*1 = 0)
```

#### 4. 向量取模运算

```glsl
// 向量与向量
vec2 result1 = mod(vec2(7.0, 8.0), vec2(3.0, 5.0));    // 返回 vec2(1.0, 3.0)
vec3 result2 = mod(vec3(10.0, 15.0, 7.0), vec3(3.0, 4.0, 2.0)); // 返回 vec3(1.0, 3.0, 1.0)

// 向量与标量
vec2 result3 = mod(vec2(7.0, 8.0), 3.0);               // 返回 vec2(1.0, 2.0)
vec3 result4 = mod(vec3(5.5, 7.2, 9.1), 2.0);         // 返回 vec3(1.5, 1.2, 1.1)
```

### mod() 函数的实际应用场景

#### 1. 纹理坐标重复 (Texture Wrapping)

```glsl
// 创建重复的纹理坐标
varying vec2 v_texCoord;
uniform sampler2D u_texture;

void main() {
    // 将纹理坐标限制在 [0, 1] 范围内，实现重复效果
    vec2 wrappedCoord = mod(v_texCoord, 1.0);
    vec4 color = texture2D(u_texture, wrappedCoord);
    gl_FragColor = color;
}

// 创建多倍重复
vec2 tiledCoord = mod(v_texCoord * 4.0, 1.0);  // 4x4 重复
```

#### 2. 周期性动画

```glsl
uniform float u_time;

void main() {
    // 创建0-1之间的周期性值，周期为2秒
    float cycle = mod(u_time, 2.0) / 2.0;

    // 创建脉冲效果
    float pulse = sin(mod(u_time * 3.14159, 6.28318));

    // 创建颜色循环
    vec3 color = vec3(
        mod(u_time * 0.5, 1.0),        // 红色分量循环
        mod(u_time * 0.3 + 0.33, 1.0), // 绿色分量循环（相位偏移）
        mod(u_time * 0.7 + 0.66, 1.0)  // 蓝色分量循环（相位偏移）
    );

    gl_FragColor = vec4(color, 1.0);
}
```

#### 3. 网格和条纹图案

```glsl
varying vec2 v_texCoord;

void main() {
    // 创建网格图案
    vec2 grid = mod(v_texCoord * 10.0, 1.0);  // 10x10 网格
    float gridLine = step(0.9, max(grid.x, grid.y));

    // 创建条纹图案
    float stripes = mod(v_texCoord.x * 20.0, 1.0);
    float stripeMask = step(0.5, stripes);

    // 创建棋盘图案
    vec2 checker = mod(floor(v_texCoord * 8.0), 2.0);
    float checkerboard = abs(checker.x - checker.y);

    gl_FragColor = vec4(vec3(checkerboard), 1.0);
}
```

#### 4. 噪声和随机化

```glsl
// 伪随机数生成器中的应用
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

// 使用mod创建周期性噪声
float periodicNoise(vec2 pos, float period) {
    vec2 wrappedPos = mod(pos, period);
    return random(wrappedPos);
}
```

#### 5. 数值范围限制

```glsl
// 将角度限制在 [0, 2π] 范围内
float normalizeAngle(float angle) {
    return mod(angle, 6.28318530718);  // 2π
}

// 将值限制在指定范围内
float wrapValue(float value, float minVal, float maxVal) {
    float range = maxVal - minVal;
    return mod(value - minVal, range) + minVal;
}

// 创建锯齿波
float sawtoothWave(float x, float frequency) {
    return mod(x * frequency, 1.0);
}
```

#### 6. 性能优化技巧

```glsl
// 使用mod避免大数值计算
uniform float u_time;

void main() {
    // 避免时间值过大导致精度问题
    float normalizedTime = mod(u_time, 100.0);  // 每100秒重置一次

    // 在动画中使用
    float wave = sin(normalizedTime * 2.0);

    gl_FragColor = vec4(vec3(wave * 0.5 + 0.5), 1.0);
}
```

### 注意事项和最佳实践

#### 1. 精度考虑

```glsl
// 在移动设备上使用适当精度
precision mediump float;

// 对于高频率的mod运算，考虑使用fract()替代
float fastMod1 = fract(value);           // 等价于 mod(value, 1.0)
float fastMod = value - floor(value);    // 手动实现，有时更快
```

#### 2. 边界情况处理

```glsl
// 避免除零错误
float safeMod(float x, float y) {
    return y != 0.0 ? mod(x, y) : 0.0;
}

// 处理负数情况
float positiveMod(float x, float y) {
    float result = mod(x, y);
    return result < 0.0 ? result + y : result;
}
```

#### round() - 四舍五入 (WebGL 2.0)

```glsl
float round(float x)
vec2 round(vec2 x)
vec3 round(vec3 x)
vec4 round(vec4 x)
```

**功能**: 返回最接近的整数值
**示例**:

```glsl
float result = round(3.7);                  // 返回 4.0
float result2 = round(3.2);                 // 返回 3.0
```

#### trunc() - 截断 (WebGL 2.0)

```glsl
float trunc(float x)
vec2 trunc(vec2 x)
vec3 trunc(vec3 x)
vec4 trunc(vec4 x)
```

**功能**: 返回去掉小数部分的整数值
**示例**:

```glsl
float result = trunc(3.7);                  // 返回 3.0
float result2 = trunc(-2.8);                // 返回 -2.0
```

### 1.2 幂函数与指数函数

#### pow() - 幂运算

```glsl
float pow(float x, float y)
vec2 pow(vec2 x, vec2 y)
vec3 pow(vec3 x, vec3 y)
vec4 pow(vec4 x, vec4 y)
```

**功能**: 返回 x 的 y 次幂
**示例**:

```glsl
float result = pow(2.0, 3.0);                   // 返回 8.0
vec3 result = pow(vec3(2.0, 3.0, 4.0), vec3(2.0)); // 返回 vec3(4.0, 9.0, 16.0)
```

#### exp() - 自然指数

```glsl
float exp(float x)
vec2 exp(vec2 x)
vec3 exp(vec3 x)
vec4 exp(vec4 x)
```

**功能**: 返回 e 的 x 次幂
**示例**:

```glsl
float result = exp(1.0);                        // 返回约 2.718
vec2 result = exp(vec2(0.0, 1.0));             // 返回 vec2(1.0, 2.718)
```

#### log() - 自然对数

```glsl
float log(float x)
vec2 log(vec2 x)
vec3 log(vec3 x)
vec4 log(vec4 x)
```

**功能**: 返回参数的自然对数
**示例**:

```glsl
float result = log(2.718);                      // 返回约 1.0
vec2 result = log(vec2(1.0, 2.718));           // 返回 vec2(0.0, 1.0)
```

#### exp2() - 2 的幂

```glsl
float exp2(float x)
vec2 exp2(vec2 x)
vec3 exp2(vec3 x)
vec4 exp2(vec4 x)
```

**功能**: 返回 2 的 x 次幂
**示例**:

```glsl
float result = exp2(3.0);                       // 返回 8.0
vec3 result = exp2(vec3(1.0, 2.0, 3.0));       // 返回 vec3(2.0, 4.0, 8.0)
```

#### log2() - 以 2 为底的对数

```glsl
float log2(float x)
vec2 log2(vec2 x)
vec3 log2(vec3 x)
vec4 log2(vec4 x)
```

**功能**: 返回以 2 为底的对数
**示例**:

```glsl
float result = log2(8.0);                       // 返回 3.0
vec3 result = log2(vec3(2.0, 4.0, 8.0));       // 返回 vec3(1.0, 2.0, 3.0)
```

#### sqrt() - 平方根

```glsl
float sqrt(float x)
vec2 sqrt(vec2 x)
vec3 sqrt(vec3 x)
vec4 sqrt(vec4 x)
```

**功能**: 返回参数的平方根
**示例**:

```glsl
float result = sqrt(16.0);                      // 返回 4.0
vec2 result = sqrt(vec2(9.0, 25.0));           // 返回 vec2(3.0, 5.0)
```

#### inversesqrt() - 平方根倒数

```glsl
float inversesqrt(float x)
vec2 inversesqrt(vec2 x)
vec3 inversesqrt(vec3 x)
vec4 inversesqrt(vec4 x)
```

**功能**: 返回 1/sqrt(x)，比 1.0/sqrt(x) 更高效
**示例**:

```glsl
float result = inversesqrt(4.0);                // 返回 0.5
vec2 result = inversesqrt(vec2(9.0, 16.0));    // 返回 vec2(0.333, 0.25)
```

### 1.3 三角函数

#### sin() - 正弦函数

```glsl
float sin(float angle)
vec2 sin(vec2 angle)
vec3 sin(vec3 angle)
vec4 sin(vec4 angle)
```

**功能**: 返回角度的正弦值（角度以弧度为单位）
**示例**:

```glsl
float result = sin(3.14159 / 2.0);              // 返回约 1.0
vec2 result = sin(vec2(0.0, 3.14159));         // 返回 vec2(0.0, 0.0)
```

#### cos() - 余弦函数

```glsl
float cos(float angle)
vec2 cos(vec2 angle)
vec3 cos(vec3 angle)
vec4 cos(vec4 angle)
```

**功能**: 返回角度的余弦值（角度以弧度为单位）
**示例**:

```glsl
float result = cos(0.0);                        // 返回 1.0
vec2 result = cos(vec2(0.0, 3.14159));         // 返回 vec2(1.0, -1.0)
```

#### tan() - 正切函数

```glsl
float tan(float angle)
vec2 tan(vec2 angle)
vec3 tan(vec3 angle)
vec4 tan(vec4 angle)
```

**功能**: 返回角度的正切值（角度以弧度为单位）
**示例**:

```glsl
float result = tan(3.14159 / 4.0);              // 返回约 1.0
vec2 result = tan(vec2(0.0, 3.14159 / 4.0));   // 返回 vec2(0.0, 1.0)
```

#### asin() - 反正弦函数

```glsl
float asin(float x)
vec2 asin(vec2 x)
vec3 asin(vec3 x)
vec4 asin(vec4 x)
```

**功能**: 返回参数的反正弦值（结果以弧度为单位）
**示例**:

```glsl
float result = asin(0.5);                       // 返回约 0.524 (30度)
vec2 result = asin(vec2(0.0, 1.0));            // 返回 vec2(0.0, 1.571)
```

#### acos() - 反余弦函数

```glsl
float acos(float x)
vec2 acos(vec2 x)
vec3 acos(vec3 x)
vec4 acos(vec4 x)
```

**功能**: 返回参数的反余弦值（结果以弧度为单位）
**示例**:

```glsl
float result = acos(0.5);                       // 返回约 1.047 (60度)
vec2 result = acos(vec2(1.0, 0.0));            // 返回 vec2(0.0, 1.571)
```

#### atan() - 反正切函数

```glsl
float atan(float y, float x)  // 两参数版本
float atan(float y_over_x)    // 单参数版本
vec2 atan(vec2 y, vec2 x)
vec3 atan(vec3 y, vec3 x)
vec4 atan(vec4 y, vec4 x)
```

**功能**: 返回参数的反正切值（结果以弧度为单位）
**示例**:

```glsl
float result = atan(1.0);                       // 返回约 0.785 (45度)
float result2 = atan(1.0, 1.0);                // 返回约 0.785 (45度)
vec2 result = atan(vec2(1.0, 0.0), vec2(1.0, 1.0)); // 返回 vec2(0.785, 0.0)
```

#### radians() - 角度转弧度

```glsl
float radians(float degrees)
vec2 radians(vec2 degrees)
vec3 radians(vec3 degrees)
vec4 radians(vec4 degrees)
```

**功能**: 将角度转换为弧度
**示例**:

```glsl
float result = radians(180.0);                  // 返回约 3.14159
vec3 result = radians(vec3(90.0, 180.0, 270.0)); // 返回 vec3(1.571, 3.142, 4.712)
```

#### degrees() - 弧度转角度

```glsl
float degrees(float radians)
vec2 degrees(vec2 radians)
vec3 degrees(vec3 radians)
vec4 degrees(vec4 radians)
```

**功能**: 将弧度转换为角度
**示例**:

```glsl
float result = degrees(3.14159);                // 返回约 180.0
vec2 result = degrees(vec2(1.571, 3.142));     // 返回 vec2(90.0, 180.0)
```

### 1.4 比较和选择函数

#### min() - 最小值

```glsl
float min(float x, float y)
vec2 min(vec2 x, vec2 y)
vec3 min(vec3 x, vec3 y)
vec4 min(vec4 x, vec4 y)
```

**功能**: 返回两个参数中的较小值
**示例**:

```glsl
float result = min(3.0, 5.0);                   // 返回 3.0
vec3 result = min(vec3(1.0, 5.0, 2.0), vec3(3.0, 2.0, 4.0)); // 返回 vec3(1.0, 2.0, 2.0)
```

#### max() - 最大值

```glsl
float max(float x, float y)
vec2 max(vec2 x, vec2 y)
vec3 max(vec3 x, vec3 y)
vec4 max(vec4 x, vec4 y)
```

**功能**: 返回两个参数中的较大值
**示例**:

```glsl
float result = max(3.0, 5.0);                   // 返回 5.0
vec3 result = max(vec3(1.0, 5.0, 2.0), vec3(3.0, 2.0, 4.0)); // 返回 vec3(3.0, 5.0, 4.0)
```

#### clamp() - 范围限制

```glsl
float clamp(float x, float minVal, float maxVal)
vec2 clamp(vec2 x, vec2 minVal, vec2 maxVal)
vec3 clamp(vec3 x, vec3 minVal, vec3 maxVal)
vec4 clamp(vec4 x, vec4 minVal, vec4 maxVal)
```

**功能**: 将值限制在指定范围内
**示例**:

```glsl
float result = clamp(1.5, 0.0, 1.0);           // 返回 1.0
vec3 color = clamp(vec3(1.2, -0.5, 0.8), 0.0, 1.0); // 返回 vec3(1.0, 0.0, 0.8)
```

#### step() - 阶跃函数

```glsl
float step(float edge, float x)
vec2 step(vec2 edge, vec2 x)
vec3 step(vec3 edge, vec3 x)
vec4 step(vec4 edge, vec4 x)
```

**功能**: 如果 x < edge 返回 0.0，否则返回 1.0
**示例**:

```glsl
float result = step(0.5, 0.3);                  // 返回 0.0
float result2 = step(0.5, 0.7);                 // 返回 1.0
vec3 result = step(vec3(0.5), vec3(0.3, 0.7, 0.5)); // 返回 vec3(0.0, 1.0, 1.0)
```

#### smoothstep() - 平滑阶跃函数

```glsl
float smoothstep(float edge0, float edge1, float x)
vec2 smoothstep(vec2 edge0, vec2 edge1, vec2 x)
vec3 smoothstep(vec3 edge0, vec3 edge1, vec3 x)
vec4 smoothstep(vec4 edge0, vec4 edge1, vec4 x)
```

**功能**: 在 edge0 和 edge1 之间进行平滑插值
**示例**:

```glsl
float result = smoothstep(0.0, 1.0, 0.5);       // 返回 0.5
float result2 = smoothstep(0.0, 1.0, 0.25);     // 返回约 0.156
vec2 result = smoothstep(vec2(0.0), vec2(1.0), vec2(0.3, 0.7)); // 返回 vec2(0.216, 0.784)
```

## 2. 向量函数 (Vector Functions)

### 2.1 向量运算

#### length() - 向量长度

```glsl
float length(float x)
float length(vec2 x)
float length(vec3 x)
float length(vec4 x)
```

**功能**: 返回向量的长度（模）
**示例**:

```glsl
float len = length(vec3(3.0, 4.0, 0.0));       // 返回 5.0
float len2 = length(vec2(1.0, 1.0));           // 返回约 1.414
```

#### distance() - 两点距离

```glsl
float distance(float p0, float p1)
float distance(vec2 p0, vec2 p1)
float distance(vec3 p0, vec3 p1)
float distance(vec4 p0, vec4 p1)
```

**功能**: 返回两点之间的距离
**示例**:

```glsl
float dist = distance(vec3(0.0, 0.0, 0.0), vec3(3.0, 4.0, 0.0)); // 返回 5.0
float dist2 = distance(vec2(0.0, 0.0), vec2(1.0, 1.0));          // 返回约 1.414
```

#### dot() - 点积

```glsl
float dot(float x, float y)
float dot(vec2 x, vec2 y)
float dot(vec3 x, vec3 y)
float dot(vec4 x, vec4 y)
```

**功能**: 返回两个向量的点积
**示例**:

```glsl
float result = dot(vec3(1.0, 0.0, 0.0), vec3(0.0, 1.0, 0.0)); // 返回 0.0
float result2 = dot(vec3(1.0, 2.0, 3.0), vec3(4.0, 5.0, 6.0)); // 返回 32.0
```

#### cross() - 叉积

```glsl
vec3 cross(vec3 x, vec3 y)
```

**功能**: 返回两个三维向量的叉积
**示例**:

```glsl
vec3 result = cross(vec3(1.0, 0.0, 0.0), vec3(0.0, 1.0, 0.0)); // 返回 vec3(0.0, 0.0, 1.0)
vec3 result2 = cross(vec3(1.0, 2.0, 3.0), vec3(4.0, 5.0, 6.0)); // 返回 vec3(-3.0, 6.0, -3.0)
```

#### normalize() - 向量归一化

```glsl
float normalize(float x)
vec2 normalize(vec2 x)
vec3 normalize(vec3 x)
vec4 normalize(vec4 x)
```

**功能**: 返回长度为 1 的同方向向量
**示例**:

```glsl
vec3 normal = normalize(vec3(3.0, 4.0, 0.0));  // 返回 vec3(0.6, 0.8, 0.0)
vec2 normal2 = normalize(vec2(1.0, 1.0));      // 返回 vec2(0.707, 0.707)
```

### 2.2 向量反射和折射

#### reflect() - 反射向量

```glsl
float reflect(float I, float N)
vec2 reflect(vec2 I, vec2 N)
vec3 reflect(vec3 I, vec3 N)
vec4 reflect(vec4 I, vec4 N)
```

**功能**: 计算入射向量相对于法向量的反射向量
**示例**:

```glsl
vec3 incident = normalize(vec3(1.0, -1.0, 0.0));
vec3 normal = vec3(0.0, 1.0, 0.0);
vec3 reflected = reflect(incident, normal);     // 返回 vec3(1.0, 1.0, 0.0)
```

#### refract() - 折射向量

```glsl
float refract(float I, float N, float eta)
vec2 refract(vec2 I, vec2 N, float eta)
vec3 refract(vec3 I, vec3 N, float eta)
vec4 refract(vec4 I, vec4 N, float eta)
```

**功能**: 计算入射向量的折射向量
**参数**: eta 是折射率比值 (n1/n2)
**示例**:

```glsl
vec3 incident = normalize(vec3(0.0, -1.0, 0.0));
vec3 normal = vec3(0.0, 1.0, 0.0);
float eta = 1.0 / 1.33; // 空气到水的折射率
vec3 refracted = refract(incident, normal, eta);
```

#### faceforward() - 面向前的法向量

```glsl
float faceforward(float N, float I, float Nref)
vec2 faceforward(vec2 N, vec2 I, vec2 Nref)
vec3 faceforward(vec3 N, vec3 I, vec3 Nref)
vec4 faceforward(vec4 N, vec4 I, vec4 Nref)
```

**功能**: 如果 dot(Nref, I) < 0 返回 N，否则返回 -N
**示例**:

```glsl
vec3 normal = vec3(0.0, 0.0, 1.0);
vec3 incident = vec3(0.0, 0.0, -1.0);
vec3 reference = vec3(0.0, 0.0, 1.0);
vec3 frontFacing = faceforward(normal, incident, reference); // 返回 vec3(0.0, 0.0, 1.0)
```

## 3. 插值和混合函数 (Interpolation Functions)

### 3.1 线性插值

#### mix() - 线性插值

```glsl
float mix(float x, float y, float a)
vec2 mix(vec2 x, vec2 y, vec2 a)
vec3 mix(vec3 x, vec3 y, vec3 a)
vec4 mix(vec4 x, vec4 y, vec4 a)
```

**功能**: 返回 x _ (1 - a) + y _ a
**示例**:

```glsl
float result = mix(0.0, 1.0, 0.5);             // 返回 0.5
vec3 color1 = vec3(1.0, 0.0, 0.0);             // 红色
vec3 color2 = vec3(0.0, 0.0, 1.0);             // 蓝色
vec3 blended = mix(color1, color2, 0.3);       // 返回 vec3(0.7, 0.0, 0.3)
```

## 4. 纹理采样函数 (Texture Functions)

### 4.1 基础纹理采样

#### texture2D() - 2D 纹理采样 (WebGL 1.0)

```glsl
vec4 texture2D(sampler2D sampler, vec2 coord)
vec4 texture2D(sampler2D sampler, vec2 coord, float bias)
```

**功能**: 从 2D 纹理中采样颜色值
**示例**:

```glsl
uniform sampler2D u_texture;
varying vec2 v_texCoord;

void main() {
    vec4 color = texture2D(u_texture, v_texCoord);
    gl_FragColor = color;
}
```

#### texture() - 通用纹理采样 (WebGL 2.0)

```glsl
vec4 texture(sampler2D sampler, vec2 P)
vec4 texture(samplerCube sampler, vec3 P)
vec4 texture(sampler3D sampler, vec3 P)
```

**功能**: 从纹理中采样颜色值（WebGL 2.0 版本）
**示例**:

```glsl
uniform sampler2D u_texture;
in vec2 v_texCoord;
out vec4 fragColor;

void main() {
    vec4 color = texture(u_texture, v_texCoord);
    fragColor = color;
}
```

#### textureCube() - 立方体纹理采样 (WebGL 1.0)

```glsl
vec4 textureCube(samplerCube sampler, vec3 coord)
vec4 textureCube(samplerCube sampler, vec3 coord, float bias)
```

**功能**: 从立方体纹理中采样颜色值
**示例**:

```glsl
uniform samplerCube u_skybox;
varying vec3 v_worldPosition;

void main() {
    vec3 direction = normalize(v_worldPosition);
    vec4 color = textureCube(u_skybox, direction);
    gl_FragColor = color;
}
```

### 4.2 纹理查询函数 (WebGL 2.0)

#### textureSize() - 纹理尺寸

```glsl
ivec2 textureSize(sampler2D sampler, int lod)
ivec3 textureSize(sampler3D sampler, int lod)
ivec2 textureSize(samplerCube sampler, int lod)
```

**功能**: 返回纹理在指定 mipmap 级别的尺寸
**示例**:

```glsl
uniform sampler2D u_texture;

void main() {
    ivec2 size = textureSize(u_texture, 0);  // 获取基础级别的纹理尺寸
    vec2 texelSize = 1.0 / vec2(size);       // 计算单个纹素的大小
}
```

#### texelFetch() - 纹素获取

```glsl
vec4 texelFetch(sampler2D sampler, ivec2 P, int lod)
vec4 texelFetch(sampler3D sampler, ivec3 P, int lod)
```

**功能**: 直接获取指定坐标的纹素值，不进行过滤
**示例**:

```glsl
uniform sampler2D u_texture;

void main() {
    ivec2 coord = ivec2(gl_FragCoord.xy);
    vec4 color = texelFetch(u_texture, coord, 0);
    fragColor = color;
}
```

## 5. 几何函数 (Geometric Functions)

### 5.1 向量操作

#### fwidth() - 偏导数宽度

```glsl
float fwidth(float p)
vec2 fwidth(vec2 p)
vec3 fwidth(vec3 p)
vec4 fwidth(vec4 p)
```

**功能**: 返回 abs(dFdx(p)) + abs(dFdy(p))
**示例**:

```glsl
varying vec2 v_texCoord;

void main() {
    float width = fwidth(v_texCoord.x);  // 获取纹理坐标的变化率
    // 用于抗锯齿等效果
}
```

#### dFdx() - X 方向偏导数

```glsl
float dFdx(float p)
vec2 dFdx(vec2 p)
vec3 dFdx(vec3 p)
vec4 dFdx(vec4 p)
```

**功能**: 返回参数在 X 方向的偏导数
**示例**:

```glsl
varying vec2 v_texCoord;

void main() {
    float dx = dFdx(v_texCoord.x);  // X方向的变化率
}
```

#### dFdy() - Y 方向偏导数

```glsl
float dFdy(float p)
vec2 dFdy(vec2 p)
vec3 dFdy(vec3 p)
vec4 dFdy(vec4 p)
```

**功能**: 返回参数在 Y 方向的偏导数
**示例**:

```glsl
varying vec2 v_texCoord;

void main() {
    float dy = dFdy(v_texCoord.y);  // Y方向的变化率
}
```

### dFdx() 和 dFdy() 详细解释

#### 重要概念澄清

您的问题涉及一个重要的概念混淆：

-   **片段着色器** ≠ **顶点着色器**
-   **片段着色器**处理的是**像素（fragments）**，不是顶点
-   **顶点着色器**处理的才是顶点

#### GPU 渲染管线中的并行处理

**1. 顶点着色器阶段**：

-   处理三角形的顶点
-   一次处理一个顶点
-   输出顶点的屏幕坐标、颜色、纹理坐标等

**2. 栅格化阶段**：

-   将三角形转换为像素
-   确定哪些像素被三角形覆盖
-   为每个被覆盖的像素生成一个**片段（fragment）**

**3. 片段着色器阶段**：

-   处理栅格化产生的每个片段（像素）
-   **关键点**：GPU 将相邻的 2×2 像素组成一个四边形同时处理
-   四个像素并行执行相同的片段着色器代码

#### 2×2 像素块并行处理机制

```
栅格化后的像素网格：
┌─────┬─────┬─────┬─────┐
│(0,0)│(1,0)│(2,0)│(3,0)│
├─────┼─────┼─────┼─────┤
│(0,1)│(1,1)│(2,1)│(3,1)│
├─────┼─────┼─────┼─────┤
│(0,2)│(1,2)│(2,2)│(3,2)│
└─────┴─────┴─────┴─────┘

GPU 将其组织为 2×2 块（Quads）：
┌─────────────┬─────────────┐
│  Quad 0     │  Quad 1     │
│ (0,0)(1,0)  │ (2,0)(3,0)  │
│ (0,1)(1,1)  │ (2,1)(3,1)  │
├─────────────┼─────────────┤
│  Quad 2     │  Quad 3     │
│ (0,2)(1,2)  │ (2,2)(3,2)  │
│ (0,3)(1,3)  │ (2,3)(3,3)  │
└─────────────┴─────────────┘
```

**每个 Quad 中的 4 个像素同时执行片段着色器**

#### 为什么需要 2×2 并行处理？

**1. 偏导数计算需求**：

```glsl
// 在片段着色器中
void main() {
    vec2 uv = v_texCoord;

    // dFdx 需要比较相邻像素的值
    float dx = dFdx(uv.x);  // 需要知道右边像素的 uv.x 值
    float dy = dFdy(uv.y);  // 需要知道下面像素的 uv.y 值
}
```

**2. 纹理 LOD 计算**：

```glsl
// texture2D 内部自动计算 mipmap 级别
vec4 color = texture2D(u_texture, uv);
// GPU 内部使用 dFdx(uv) 和 dFdy(uv) 来选择合适的 mipmap 级别
```

#### 实际执行过程

**步骤 1：栅格化**

```
三角形 → 栅格化 → 生成片段（像素）
```

**步骤 2：分组执行**

```
片段分组为 2×2 块 → 每个块并行执行片段着色器
```

**步骤 3：偏导数计算**

```
Quad 中的像素：
┌─────┬─────┐
│ A   │ B   │  dFdx(A) = B - A
├─────┼─────┤  dFdy(A) = C - A
│ C   │ D   │
└─────┴─────┘
```

#### 计算方法详解

```glsl
// 假设 2×2 块中的纹理坐标值：
// A(0,0): uv = (0.1, 0.2)
// B(1,0): uv = (0.3, 0.2)
// C(0,1): uv = (0.1, 0.4)
// D(1,1): uv = (0.3, 0.4)

// 对于像素 A：
float dx = dFdx(uv.x);  // = 0.3 - 0.1 = 0.2
float dy = dFdy(uv.y);  // = 0.4 - 0.2 = 0.2
```

#### 关键要点总结

1. **片段着色器处理像素，不是顶点**
2. **GPU 以 2×2 像素块为单位并行执行片段着色器**
3. **偏导数通过比较相邻像素的值来计算**
4. **这种机制使得纹理采样和抗锯齿成为可能**

## 6. 矩阵函数 (Matrix Functions)

### 6.1 矩阵运算

#### matrixCompMult() - 矩阵分量乘法

```glsl
mat2 matrixCompMult(mat2 x, mat2 y)
mat3 matrixCompMult(mat3 x, mat3 y)
mat4 matrixCompMult(mat4 x, mat4 y)
```

**功能**: 对应分量相乘（不是矩阵乘法）
**示例**:

```glsl
mat3 m1 = mat3(1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0);
mat3 m2 = mat3(2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0);
mat3 result = matrixCompMult(m1, m2);  // 每个分量都乘以2
```

#### transpose() - 矩阵转置 (WebGL 2.0)

```glsl
mat2 transpose(mat2 m)
mat3 transpose(mat3 m)
mat4 transpose(mat4 m)
```

**功能**: 返回矩阵的转置
**示例**:

```glsl
mat3 original = mat3(1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0);
mat3 transposed = transpose(original);
```

#### determinant() - 矩阵行列式 (WebGL 2.0)

```glsl
float determinant(mat2 m)
float determinant(mat3 m)
float determinant(mat4 m)
```

**功能**: 返回矩阵的行列式
**示例**:

```glsl
mat3 matrix = mat3(1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0);
float det = determinant(matrix);  // 返回 1.0
```

#### inverse() - 矩阵求逆 (WebGL 2.0)

```glsl
mat2 inverse(mat2 m)
mat3 inverse(mat3 m)
mat4 inverse(mat4 m)
```

**功能**: 返回矩阵的逆矩阵
**示例**:

```glsl
mat4 modelMatrix = mat4(/* ... */);
mat4 inverseModel = inverse(modelMatrix);
```

## 7. 整数函数 (Integer Functions) - WebGL 2.0

### 7.1 位运算

#### floatBitsToInt() - 浮点数转整数位

```glsl
int floatBitsToInt(float value)
ivec2 floatBitsToInt(vec2 value)
ivec3 floatBitsToInt(vec3 value)
ivec4 floatBitsToInt(vec4 value)
```

**功能**: 将浮点数的位模式解释为整数
**示例**:

```glsl
float f = 1.0;
int bits = floatBitsToInt(f);  // 获取浮点数的位表示
```

#### intBitsToFloat() - 整数位转浮点数

```glsl
float intBitsToFloat(int value)
vec2 intBitsToFloat(ivec2 value)
vec3 intBitsToFloat(ivec3 value)
vec4 intBitsToFloat(ivec4 value)
```

**功能**: 将整数的位模式解释为浮点数
**示例**:

```glsl
int bits = 0x3F800000;  // 1.0的IEEE 754表示
float f = intBitsToFloat(bits);  // 返回 1.0
```

## 8. 实用示例

### 8.1 常用组合函数

#### 平滑过渡函数

```glsl
// 创建平滑的边缘过渡
float smoothEdge(float edge, float value, float smoothness) {
    return smoothstep(edge - smoothness, edge + smoothness, value);
}

// 使用示例
void main() {
    float dist = length(gl_FragCoord.xy - vec2(100.0, 100.0));
    float alpha = 1.0 - smoothEdge(50.0, dist, 5.0);
    gl_FragColor = vec4(1.0, 1.0, 1.0, alpha);
}
```

#### 颜色混合函数

```glsl
// 多种颜色混合模式
vec3 blendOverlay(vec3 base, vec3 blend) {
    return mix(
        2.0 * base * blend,
        1.0 - 2.0 * (1.0 - base) * (1.0 - blend),
        step(0.5, base)
    );
}

// 使用示例
void main() {
    vec3 baseColor = vec3(0.8, 0.2, 0.2);
    vec3 blendColor = vec3(0.2, 0.8, 0.8);
    vec3 result = blendOverlay(baseColor, blendColor);
    gl_FragColor = vec4(result, 1.0);
}
```

#### 噪声函数

```glsl
// 简单的伪随机噪声
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

// 平滑噪声
float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);

    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));

    vec2 u = f * f * (3.0 - 2.0 * f);

    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}
```

## 9. 性能优化建议

### 9.1 函数选择建议

1. **使用内置函数**: 内置函数经过 GPU 优化，比自定义实现更高效
2. **避免分支**: 使用 `step()` 和 `smoothstep()` 代替 `if` 语句
3. **向量化操作**: 尽可能使用向量版本的函数
4. **精度考虑**: 在移动设备上考虑使用 `mediump` 精度

### 9.2 常见优化技巧

```glsl
// 优化前：使用条件分支
if (value > 0.5) {
    result = 1.0;
} else {
    result = 0.0;
}

// 优化后：使用step函数
result = step(0.5, value);

// 优化前：复杂的距离计算
float dist = sqrt(pow(x, 2.0) + pow(y, 2.0));

// 优化后：使用内置函数
float dist = length(vec2(x, y));
```

## 总结

GLSL 内置函数是 GPU 编程的基础工具，掌握这些函数的正确使用方法对于编写高效的着色器程序至关重要。本文档涵盖了从基础数学函数到高级纹理采样的各个方面，为 GLSL 编程提供了全面的参考。

在实际开发中，建议：

1. 优先使用内置函数而非自定义实现
2. 注意 WebGL 版本差异
3. 考虑性能和精度平衡
4. 多实践和测试不同函数的效果
